/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        
        // Custom DOAXVV colors
        'dark-primary': '#1E1E1E',
        'dark-secondary': '#0D0D0D',
        'dark-card': '#2A2A2A',
        'dark-border': '#404040',
        
        // Accent colors - work well in both themes
        'accent-pink': '#FF4FAD',
        'accent-cyan': '#4FFFD9',
        'accent-purple': '#BD4FFF',
        'accent-gold': '#FFC24F',
        
        // Game stat colors - WCAG AA compliant variants
        'stat-pow': '#E91E63', // Enhanced contrast for pink
        'stat-tec': '#00BCD4', // Enhanced contrast for cyan
        'stat-stm': '#FF9800', // Enhanced contrast for orange
        'stat-apl': '#9C27B0', // Enhanced contrast for purple

        // Light theme variants with better contrast
        'light-primary': '#F8FAFC',
        'light-secondary': '#E2E8F0',
        'light-card': '#FFFFFF',
        'light-border': '#CBD5E1',

        // Accessibility-focused colors
        'focus-ring': '#3B82F6',
        'error-text': '#DC2626',
        'success-text': '#059669',
        'warning-text': '#D97706',
        'info-text': '#0284C7',
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        'sans': ['Inter', 'Noto Sans JP', 'Noto Sans SC', 'sans-serif'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1.5' }],
        'sm': ['0.875rem', { lineHeight: '1.5' }],
        'base': ['1rem', { lineHeight: '1.5' }],
        'lg': ['1.125rem', { lineHeight: '1.6' }],
        'xl': ['1.25rem', { lineHeight: '1.6' }],
        '2xl': ['1.5rem', { lineHeight: '1.6' }],
        '3xl': ['1.875rem', { lineHeight: '1.6' }],
        '4xl': ['2.25rem', { lineHeight: '1.6' }],
        '5xl': ['3rem', { lineHeight: '1.6' }],
        '6xl': ['3.75rem', { lineHeight: '1.6' }],
        // Accessibility-focused sizes
        'readable': ['max(1rem, 16px)', { lineHeight: '1.5' }],
        'large-readable': ['max(1.125rem, 18px)', { lineHeight: '1.6' }],
      },
      spacing: {
        '11': '2.75rem', // 44px - minimum touch target
        '18': '4.5rem',  // 72px
        '22': '5.5rem',  // 88px
      },
      backdropBlur: {
        xs: '2px',
      },
      animation: {
        'glow': 'glow 2s ease-in-out infinite alternate',
        'fade-in': 'fade-in 0.3s ease-out',
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      keyframes: {
        glow: {
          '0%': { boxShadow: '0 0 5px currentColor' },
          '100%': { boxShadow: '0 0 20px currentColor, 0 0 30px currentColor' },
        },
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      }
    },
  },
  plugins: [require("tailwindcss-animate")],
} 