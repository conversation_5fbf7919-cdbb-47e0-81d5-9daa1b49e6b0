@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced Dark Theme - WCAG AA Compliant */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 6.5%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 6.5%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 19.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 19.5%;
    --muted-foreground: 215 20.2% 70.1%;
    --accent: 217.2 32.6% 19.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 19.5%;
    --input: 217.2 32.6% 19.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;

    /* Enhanced Layout Variables */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 64px;
    --header-height: 64px;
    --content-padding: 24px;

    /* Accessibility Variables */
    --min-touch-target: 44px;
    --min-icon-size: 24px;
    --min-font-size: 16px;
    --optimal-line-height: 1.5;
    --focus-ring-width: 2px;
    --focus-ring-offset: 2px;

    /* Animation Variables */
    --theme-transition-duration: 0.3s;
    --theme-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
    --hover-transition-duration: 0.2s;
    --focus-transition-duration: 0.15s;
  }

  .light {
    /* Enhanced Light Theme - WCAG AA Compliant */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 98%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 98%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 94%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 94%;
    --muted-foreground: 215.4 16.3% 40.9%;
    --accent: 210 40% 94%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 45.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 88.4%;
    --input: 214.3 31.8% 88.4%;
    --ring: 222.2 84% 4.9%;
  }

  * {
    @apply border-border;
    transition:
      border-color var(--theme-transition-duration) var(--theme-transition-easing),
      background-color var(--theme-transition-duration) var(--theme-transition-easing),
      color var(--theme-transition-duration) var(--theme-transition-easing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-easing);
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: var(--optimal-line-height);
    font-size: var(--min-font-size);
    min-height: 100vh;
  }

  /* Enhanced Background Gradients */
  .light body {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
  }

  .dark body,
  body {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  }

  /* Focus Management for Accessibility */
  *:focus-visible {
    outline: var(--focus-ring-width) solid hsl(var(--ring));
    outline-offset: var(--focus-ring-offset);
    border-radius: var(--radius);
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@layer components {
  .compact-container {
    @apply max-w-none px-4 py-3;
  }
  
  .compact-card {
    @apply bg-card/80 backdrop-blur-sm border border-border/50 rounded-lg;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }
  
  .glass-effect {
    @apply bg-card/70 backdrop-blur-md border border-border/30;
    box-shadow: 
      0 4px 6px -1px rgb(0 0 0 / 0.1),
      0 2px 4px -2px rgb(0 0 0 / 0.1),
      inset 0 1px 0 rgb(255 255 255 / 0.1);
  }
  
  .light .glass-effect {
    box-shadow: 
      0 4px 6px -1px rgb(0 0 0 / 0.05),
      0 2px 4px -2px rgb(0 0 0 / 0.05),
      inset 0 1px 0 rgb(255 255 255 / 0.8);
  }
  
  .minimal-transition {
    @apply transition-all duration-200 ease-out;
  }
  
  .hover-lift {
    @apply transform-gpu;
    transition: transform 0.2s ease-out;
  }
  
  .hover-lift:hover {
    @apply -translate-y-0.5;
  }
  
  .stat-bar {
    @apply h-1.5 rounded-full transition-all duration-300;
  }
  
  .compact-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .compact-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  .compact-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-border/50 rounded-full;
  }
  
  .compact-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-border;
  }
  
  .compact-input {
    @apply h-8 px-3 text-sm rounded-md border border-input bg-background;
  }
  
  .compact-button {
    @apply h-8 px-3 text-sm rounded-md inline-flex items-center justify-center;
  }
  
  .theme-sync {
    transition: 
      background-color 0.2s ease-out,
      border-color 0.2s ease-out,
      color 0.2s ease-out,
      box-shadow 0.2s ease-out;
  }

  /* Compact Layout Utilities */
  .compact-grid {
    @apply grid gap-3;
  }
  
  .compact-flex {
    @apply flex items-center space-x-2;
  }
  
  .compact-text {
    @apply text-sm leading-tight;
  }
  
  .compact-text-xs {
    @apply text-xs leading-tight;
  }
  
  .compact-spacing {
    @apply space-y-2;
  }
  
  .compact-padding {
    @apply p-3;
  }
  
  .compact-margin {
    @apply m-2;
  }

  /* Content Density Utilities */
  .dense-layout {
    @apply space-y-3;
  }
  
  .dense-grid {
    @apply grid gap-2;
  }
  
  .dense-card {
    @apply compact-card p-3;
  }
  
  .dense-header {
    @apply pb-2 mb-2 border-b border-border/30;
  }

  /* Modern Visual Effects */
  .modern-border {
    @apply border border-border/30 rounded-lg;
  }
  
  .modern-shadow {
    box-shadow: 
      0 1px 3px 0 rgb(0 0 0 / 0.1),
      0 1px 2px -1px rgb(0 0 0 / 0.1);
  }
  
  .light .modern-shadow {
    box-shadow: 
      0 1px 3px 0 rgb(0 0 0 / 0.05),
      0 1px 2px -1px rgb(0 0 0 / 0.05);
  }
  
  .accent-glow {
    box-shadow: 0 0 0 1px currentColor;
  }

  /* Enhanced Accessibility Utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .touch-target {
    min-width: var(--min-touch-target);
    min-height: var(--min-touch-target);
  }

  .icon-size {
    min-width: var(--min-icon-size);
    min-height: var(--min-icon-size);
  }

  /* Enhanced Interactive States */
  .interactive-element {
    @apply transition-all duration-200 ease-out;
    @apply hover:scale-[1.02] active:scale-[0.98];
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .interactive-element:hover {
    transform: translateY(-1px);
  }

  .interactive-element:active {
    transform: translateY(0);
  }

  /* Theme Transition Enhancement */
  .theme-transitioning * {
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-easing),
      border-color var(--theme-transition-duration) var(--theme-transition-easing),
      color var(--theme-transition-duration) var(--theme-transition-easing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-easing) !important;
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 20%;
      --ring: 0 0% 80%;
    }

    .light {
      --border: 0 0% 80%;
      --ring: 0 0% 20%;
    }
  }

  /* Typography Enhancements */
  .text-readable {
    font-size: max(var(--min-font-size), 1rem);
    line-height: var(--optimal-line-height);
  }

  .text-large-readable {
    font-size: max(18px, 1.125rem);
    line-height: 1.6;
  }

  /* Enhanced Card Styles */
  .enhanced-card {
    @apply compact-card;
    @apply hover:shadow-lg hover:border-border/70;
    @apply transition-all duration-300 ease-out;
    @apply focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2;
  }

  .enhanced-card:hover {
    transform: translateY(-2px);
  }

  /* Font Size Accessibility Classes */
  .font-large {
    --min-font-size: 18px;
  }

  .font-large .text-sm {
    font-size: max(16px, 0.875rem);
  }

  .font-large .text-base {
    font-size: max(18px, 1rem);
  }

  .font-extra-large {
    --min-font-size: 20px;
  }

  .font-extra-large .text-sm {
    font-size: max(18px, 0.875rem);
  }

  .font-extra-large .text-base {
    font-size: max(20px, 1rem);
  }

  /* Reduced Motion Classes */
  .reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* High Contrast Mode */
  .high-contrast {
    --border: 0 0% 30%;
    --ring: 0 0% 70%;
  }

  .high-contrast.light {
    --border: 0 0% 70%;
    --ring: 0 0% 30%;
  }

  /* Focus Management */
  .focus-visible-only:focus:not(:focus-visible) {
    outline: none;
  }

  /* Enhanced Button States */
  button:focus-visible,
  [role="button"]:focus-visible,
  a:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: var(--radius);
  }

  /* Loading States */
  .loading {
    position: relative;
    overflow: hidden;
  }

  .loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    animation: loading-shimmer 1.5s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  /* Print Styles */
  @media print {
    .no-print {
      display: none !important;
    }

    * {
      background: white !important;
      color: black !important;
      box-shadow: none !important;
    }
  }
}