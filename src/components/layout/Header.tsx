import { useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ChevronRight, Filter, Settings, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ThemeToggle } from '../shared';

const routeNames: Record<string, string> = {
  '/': 'nav.home',
  '/swimsuit': 'nav.swimsuit',
  '/girls': 'nav.girl',
  '/calculator/parameter': 'nav.parameterCalculator',
  '/calculator/exp': 'nav.expCalculator',
  '/settings': 'nav.settings',
  '/skills': 'nav.skill',
  '/data/passive-skill': 'nav.passiveSkill',
  '/data/decorate-bromide': 'nav.decorateBromide',
  '/data/accessory': 'nav.accessory',
  '/data/level': 'nav.level',
  '/data/owner-room': 'nav.ownerRoom',
  '/data/monthly-battle': 'nav.monthlyBattle',
  '/tool/messages': 'nav.messages',
};

export default function Header() {
  const { t } = useTranslation();
  const location = useLocation();
  
  const getCurrentPageName = () => {
    return routeNames[location.pathname] || 'nav.home';
  };

  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [{ name: t('nav.home'), path: '/' }];
    
    let currentPath = '';
    pathSegments.forEach((segment) => {
      currentPath += `/${segment}`;
      const routeName = routeNames[currentPath];
      if (routeName) {
        breadcrumbs.push({ name: t(routeName), path: currentPath });
      }
    });
    
    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <header 
      className="glass-effect border-b border-border/50 px-4 py-2 theme-sync"
      style={{ height: 'var(--header-height)' }}
    >
      <div className="flex items-center justify-between h-full">
        {/* Left side - Compact Breadcrumbs */}
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          <nav className="flex items-center space-x-1 text-xs">
            {breadcrumbs.map((crumb, index) => (
              <div key={crumb.path} className="flex items-center">
                {index > 0 && (
                  <ChevronRight className="w-3 h-3 text-muted-foreground mx-1" />
                )}
                <span
                  className={`${
                    index === breadcrumbs.length - 1
                      ? 'text-foreground font-medium'
                      : 'text-muted-foreground hover:text-foreground cursor-pointer minimal-transition'
                  } truncate`}
                >
                  {crumb.name}
                </span>
              </div>
            ))}
          </nav>
        </div>

        {/* Right side - Enhanced Actions */}
        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <Input
              type="text"
              placeholder={t('common.search')}
              className="h-10 pl-10 pr-4 w-56 text-base border-border/50 bg-background/50 backdrop-blur-sm focus:bg-background focus:border-ring transition-all duration-200"
              style={{ minHeight: 'var(--min-touch-target)' }}
            />
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
              style={{ minWidth: 'var(--min-icon-size)', minHeight: 'var(--min-icon-size)' }}
            />
          </div>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Filter */}
          <Button
            variant="outline"
            size="touch-icon"
            className="hover:bg-accent/80 focus-visible:ring-2 focus-visible:ring-ring"
            aria-label="Filter options"
          >
            <Filter
              className="w-5 h-5"
              style={{ minWidth: 'var(--min-icon-size)', minHeight: 'var(--min-icon-size)' }}
            />
          </Button>

          {/* Settings */}
          <Button
            variant="outline"
            size="touch-icon"
            className="hover:bg-accent/80 focus-visible:ring-2 focus-visible:ring-ring"
            asChild
          >
            <Link to="/settings" aria-label="Settings">
              <Settings
                className="w-5 h-5"
                style={{ minWidth: 'var(--min-icon-size)', minHeight: 'var(--min-icon-size)' }}
              />
            </Link>
          </Button>
        </div>
      </div>
    </header>
  );
} 