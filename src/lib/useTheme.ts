import { useEffect, useCallback } from 'react';
import { useAppStore } from '@/store';

type ThemeMode = 'dark' | 'light' | 'system';

export function useTheme() {
  const { theme, setTheme } = useAppStore();

  // Detect system theme preference
  const getSystemTheme = useCallback((): 'dark' | 'light' => {
    if (typeof window === 'undefined') return 'dark';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }, []);

  // Initialize theme on first load
  useEffect(() => {
    const savedTheme = localStorage.getItem('doaxvv-theme') as ThemeMode;
    const systemTheme = getSystemTheme();

    if (!savedTheme) {
      // First time user - use system preference
      setTheme(systemTheme);
      localStorage.setItem('doaxvv-theme', systemTheme);
    } else if (savedTheme === 'system') {
      // User prefers system theme
      setTheme(systemTheme);
    } else if (savedTheme !== theme) {
      // Use saved preference
      setTheme(savedTheme as 'dark' | 'light');
    }
  }, [getSystemTheme, setTheme, theme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      const savedTheme = localStorage.getItem('doaxvv-theme');
      if (savedTheme === 'system' || !savedTheme) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [setTheme]);

  // Apply theme to document on mount and when theme changes
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;

    // Remove existing theme classes
    root.classList.remove('dark', 'light');
    body.classList.remove('dark', 'light');

    // Add current theme class to both root and body for better sync
    root.classList.add(theme);
    body.classList.add(theme);

    // Update meta theme-color for mobile browsers with enhanced colors
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content',
        theme === 'dark' ? '#0f0f23' : '#ffffff'
      );
    }

    // Update CSS custom properties for enhanced transitions
    root.style.setProperty(
      '--theme-transition',
      'background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    );

    // Enhanced color scheme support
    root.style.colorScheme = theme;

    // Add smooth transition class temporarily
    root.classList.add('theme-transitioning');
    setTimeout(() => {
      root.classList.remove('theme-transitioning');
    }, 300);

  }, [theme]);

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);

    // Store preference in localStorage for persistence
    localStorage.setItem('doaxvv-theme', newTheme);

    // Announce theme change for screen readers
    const announcement = `Theme switched to ${newTheme} mode`;
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    document.body.appendChild(announcer);
    setTimeout(() => document.body.removeChild(announcer), 1000);
  }, [theme, setTheme]);

  const setSystemTheme = useCallback(() => {
    const systemTheme = getSystemTheme();
    setTheme(systemTheme);
    localStorage.setItem('doaxvv-theme', 'system');
  }, [getSystemTheme, setTheme]);

  const isDark = theme === 'dark';
  const isLight = theme === 'light';
  const systemTheme = getSystemTheme();

  return {
    theme,
    setTheme,
    toggleTheme,
    setSystemTheme,
    isDark,
    isLight,
    systemTheme,
    getSystemTheme,
  };
}