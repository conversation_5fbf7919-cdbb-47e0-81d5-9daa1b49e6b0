import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { useAppStore } from './store';
import { useTheme } from './lib/useTheme';
import { useDatabase } from './lib/useDatabase';

// Layout Components
import { Sidebar, Header } from './components/layout';
import { AccessibilityProvider, SkipLink } from './components/shared/AccessibilityProvider';

// All Pages
import {
  HomePage,
  SwimsuitPage,
  ParameterCalculatorPage,
  ExpCalculatorPage,
  GirlListPage,
  SettingsPage,
  SkillsPage,
  AccessoryPage,
  PassiveSkillPage,
  LevelPage,
  DecorateBromidePage,
  OwnerRoomPage,
  MonthlyBattlePage,
  MessagesPage
} from './pages';

function App() {
  const { i18n } = useTranslation();
  const { currentLanguage, sidebarCollapsed } = useAppStore();
  
  // Use theme hook to handle theme application
  useTheme();
  
  // Initialize database
  useDatabase();

  useEffect(() => {
    i18n.changeLanguage(currentLanguage);
  }, [currentLanguage, i18n]);

  return (
    <AccessibilityProvider>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <div className="min-h-screen bg-background theme-sync">
          {/* Skip Links for Accessibility */}
          <SkipLink href="#main-content">Skip to main content</SkipLink>
          <SkipLink href="#sidebar-nav">Skip to navigation</SkipLink>

          {/* Enhanced Background Pattern with reduced opacity for better readability */}
          <div className="fixed inset-0 opacity-[0.015] pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-br from-accent-pink/8 via-accent-cyan/8 to-accent-purple/8" />
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(233, 30, 99, 0.08) 0%, transparent 25%),
                               radial-gradient(circle at 75% 75%, rgba(0, 188, 212, 0.08) 0%, transparent 25%)`
            }} />
          </div>

          <div className="relative flex min-h-screen">
            {/* Sidebar with enhanced accessibility */}
            <nav id="sidebar-nav" aria-label="Main navigation">
              <Sidebar />
            </nav>

            {/* Main Content with enhanced accessibility */}
            <div
              className="flex-1 minimal-transition"
              style={{
                marginLeft: sidebarCollapsed ? 'var(--sidebar-collapsed-width)' : 'var(--sidebar-width)'
              }}
            >
              <Header />
              <main
                id="main-content"
                className="compact-container focus:outline-none"
                style={{ padding: 'var(--content-padding)' }}
                tabIndex={-1}
                role="main"
                aria-label="Main content"
              >
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/swimsuit" element={<SwimsuitPage />} />
                  <Route path="/girls" element={<GirlListPage />} />
                  <Route path="/skills" element={<SkillsPage />} />
                  <Route path="/data/passive-skill" element={<PassiveSkillPage />} />
                  <Route path="/data/decorate-bromide" element={<DecorateBromidePage />} />
                  <Route path="/data/accessory" element={<AccessoryPage />} />
                  <Route path="/data/level" element={<LevelPage />} />
                  <Route path="/data/owner-room" element={<OwnerRoomPage />} />
                  <Route path="/data/monthly-battle" element={<MonthlyBattlePage />} />
                  <Route path="/tool/messages" element={<MessagesPage />} />
                  <Route path="/calculator/parameter" element={<ParameterCalculatorPage />} />
                  <Route path="/calculator/exp" element={<ExpCalculatorPage />} />
                  <Route path="/settings" element={<SettingsPage />} />
                </Routes>
              </main>
            </div>
          </div>
        </div>
      </Router>
    </AccessibilityProvider>
  );
}

export default App; 