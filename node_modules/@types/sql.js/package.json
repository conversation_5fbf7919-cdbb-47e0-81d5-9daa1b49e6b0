{"name": "@types/sql.js", "version": "1.4.9", "description": "TypeScript definitions for sql.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sql.js", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian", "url": "https://github.com/ffflorian"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "pastelmind", "url": "https://github.com/pastelmind"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sql.js"}, "scripts": {}, "dependencies": {"@types/emscripten": "*", "@types/node": "*"}, "typesPublisherContentHash": "6efedc1f80f72bf98efda9810e94100d91be2af06c239ee15ef776d4fff89466", "typeScriptVersion": "4.5"}