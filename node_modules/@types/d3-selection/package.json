{"name": "@types/d3-selection", "version": "3.0.11", "description": "TypeScript definitions for d3-selection", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-selection", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "githubUsername": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "<PERSON><PERSON>", "githubUsername": "ambar-<PERSON><PERSON>", "url": "https://github.com/ambar-arkin"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-selection"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "fefb1bdeb8d385251c2a5f0f16c4a924e80663081d09b1d98f79573a4db8ff6c", "typeScriptVersion": "4.8"}