{"version": 3, "file": "edges.d.ts", "sourceRoot": "", "sources": ["../../../../packages/core/src/types/edges.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,IAAI,eAAe,EAAE,MAAM,OAAO,CAAC;AAEpH,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AAC/C,OAAO,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;AAErE,KAAK,gBAAgB,GAAG;IACtB,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,UAAU,CAAC,EAAE,aAAa,CAAC;IAC3B,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,YAAY,CAAC,EAAE,aAAa,CAAC;IAC7B,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClC,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAC9B,CAAC;AAGF,KAAK,WAAW,CAAC,CAAC,GAAG,GAAG,IAAI;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7B,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7B,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,IAAI,CAAC,EAAE,CAAC,CAAC;IACT,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,WAAW,CAAC,EAAE,cAAc,CAAC;IAC7B,SAAS,CAAC,EAAE,cAAc,CAAC;IAC3B,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,SAAS,CAAC,EAAE,aAAa,CAAC;IAC1B,aAAa,CAAC,EAAE,OAAO,GAAG,UAAU,CAAC;CACtC,GAAG,gBAAgB,CAAC;AAErB,MAAM,MAAM,aAAa,GAAG,OAAO,GAAG,UAAU,CAAC;AAEjD,MAAM,MAAM,qBAAqB,GAAG;IAClC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB,CAAC;AAEF,KAAK,kBAAkB,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG;IAC5C,IAAI,EAAE,YAAY,CAAC;IACnB,WAAW,CAAC,EAAE,qBAAqB,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF,KAAK,cAAc,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG;IACxC,IAAI,EAAE,SAAS,CAAC;IAChB,WAAW,CAAC,EAAE,iBAAiB,CAAC;CACjC,CAAC;AAEF,MAAM,MAAM,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AAEvF,MAAM,MAAM,kBAAkB,GAAG,IAAI,CACnC,IAAI,EACJ,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,cAAc,GAAG,cAAc,GAAG,YAAY,GAAG,YAAY,CAC3F,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;AAE5E,MAAM,MAAM,aAAa,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,GAAG,cAAc,CAAC,GAAG;IACpF,OAAO,CAAC,EAAE,gBAAgB,CAAC;IAC3B,iBAAiB,CAAC,EAAE,gBAAgB,CAAC;IACrC,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,cAAc,EAAE,QAAQ,CAAC;IACzB,cAAc,EAAE,QAAQ,CAAC;IACzB,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAC/B,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,KAAK,IAAI,CAAC;IACxF,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,KAAK,IAAI,CAAC;IAC9F,aAAa,CAAC,EAAE,gBAAgB,CAAC;IACjC,YAAY,CAAC,EAAE,gBAAgB,CAAC;IAChC,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAC/B,YAAY,CAAC,EAAE,gBAAgB,CAAC;IAChC,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,OAAO,CAAC;IACrB,eAAe,EAAE,aAAa,CAAC;IAC/B,WAAW,CAAC,EAAE,iBAAiB,GAAG,qBAAqB,CAAC;IACxD,mBAAmB,CAAC,EAAE,OAAO,CAAC;CAC/B,CAAC;AAGF,MAAM,MAAM,SAAS,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CACnC,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,OAAO,GAAG,UAAU,GAAG,QAAQ,GAAG,QAAQ,CACxE,GACC,IAAI,CACF,aAAa,EACX,SAAS,GACT,SAAS,GACT,SAAS,GACT,SAAS,GACT,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB,GAChB,kBAAkB,CACrB,GACD,gBAAgB,GAAG;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,GAAG,CAAC;CACnB,CAAC;AAEJ,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,OAAO,GAAG,aAAa,GAAG,WAAW,GAAG,kBAAkB,CAAC,GACrG,gBAAgB,GAAG;IACjB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEJ,MAAM,MAAM,mBAAmB,CAAC,CAAC,GAAG,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG;IACxD,WAAW,CAAC,EAAE,qBAAqB,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG;IACpD,WAAW,CAAC,EAAE,iBAAiB,CAAC;CACjC,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG,cAAc,CAAC,UAAU,CAAC,GACpD,gBAAgB,GAAG;IACjB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACX,CAAC;AAEJ,oBAAY,kBAAkB;IAC5B,MAAM,YAAY;IAClB,QAAQ,aAAa;IACrB,IAAI,SAAS;IACb,UAAU,eAAe;IACzB,YAAY,iBAAiB;CAC9B;AAED,MAAM,MAAM,4BAA4B,GAAG;IACzC,mBAAmB,CAAC,EAAE,aAAa,CAAC;IACpC,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,QAAQ,CAAC,EAAE,IAAI,CAAC;IAChB,UAAU,CAAC,EAAE,aAAa,CAAC;IAC3B,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,YAAY,EAAE,QAAQ,CAAC;IACvB,UAAU,EAAE,QAAQ,CAAC;IACrB,gBAAgB,EAAE,gBAAgB,GAAG,IAAI,CAAC;CAC3C,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG,aAAa,CAAC,4BAA4B,CAAC,CAAC;AAElF,MAAM,MAAM,gBAAgB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,UAAU,KAAK,IAAI,CAAC;AAE9F,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,UAAU,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,MAAM,GAAG,UAAU,CAAC;AAEjD,oBAAY,UAAU;IACpB,KAAK,UAAU;IACf,WAAW,gBAAgB;CAC5B"}