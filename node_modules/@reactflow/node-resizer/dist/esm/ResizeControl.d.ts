import React from 'react';
import { ResizeControlProps, ResizeControlLineProps } from './types';
declare function ResizeControl({ nodeId, position, variant, className, style, children, color, minWidth, minHeight, maxWidth, maxHeight, keepAspectRatio, shouldResize, onResizeStart, onResize, onResizeEnd, }: ResizeControlProps): JSX.Element;
export declare function ResizeControlLine(props: ResizeControlLineProps): JSX.Element;
declare const _default: React.MemoExoticComponent<typeof ResizeControl>;
export default _default;
//# sourceMappingURL=ResizeControl.d.ts.map